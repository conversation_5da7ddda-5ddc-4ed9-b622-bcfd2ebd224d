"""
🚀 TruBackend Comprehensive Gradio Interface

Advanced web interface for TruBackend management with:
- Real-time monitoring dashboard
- Email analysis interface
- System management controls
- Performance analytics
- AI model interaction
- Configuration management
"""

import asyncio
import logging
import json
import time
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import os
import requests
import threading

# Gradio imports
import gradio as gr

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
TRUBACKEND_API_URL = "http://localhost:8000"
GRADIO_PORT = 7860
UPDATE_INTERVAL = 5  # seconds

class TruBackendGradioInterface:
    """Comprehensive Gradio interface for TruBackend management"""
    
    def __init__(self):
        self.stats_history = []
        self.email_history = []
        self.system_logs = []
        self.is_monitoring = False
        self.monitoring_thread = None        
        # Initialize data storage
        self.performance_data = {
            'timestamps': [],
            'requests_processed': [],
            'avg_processing_time': [],
            'total_tokens': [],
            'memory_usage': [],
            'cpu_usage': []
        }
        
    def start_monitoring(self):
        """Start background monitoring thread"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            logger.info("Monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1)
        logger.info("Monitoring stopped")
    
    def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_monitoring:
            try:
                self._collect_stats()
                time.sleep(UPDATE_INTERVAL)
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(UPDATE_INTERVAL)
    
    def _collect_stats(self):
        """Collect statistics from TruBackend API"""
        try:
            response = requests.get(f"{TRUBACKEND_API_URL}/stats", timeout=5)
            if response.status_code == 200:
                stats = response.json()
                timestamp = datetime.now()
                
                # Store in history
                self.stats_history.append({
                    'timestamp': timestamp,
                    'stats': stats
                })
                
                # Update performance data
                self.performance_data['timestamps'].append(timestamp)
                self.performance_data['requests_processed'].append(
                    stats.get('processor_stats', {}).get('requests_processed', 0)
                )
                self.performance_data['avg_processing_time'].append(
                    stats.get('processor_stats', {}).get('avg_processing_time', 0)
                )
                self.performance_data['total_tokens'].append(
                    stats.get('processor_stats', {}).get('total_tokens', 0)
                )        
        # Initialize data storage
        self.performance_data = {
            'timestamps': [],
            'requests_processed': [],
            'avg_processing_time': [],
            'total_tokens': [],
            'memory_usage': [],
            'cpu_usage': []
        }
        
    def start_monitoring(self):
        """Start background monitoring thread"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            logger.info("Monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1)
        logger.info("Monitoring stopped")
    
    def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_monitoring:
            try:
                self._collect_stats()
                time.sleep(UPDATE_INTERVAL)
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(UPDATE_INTERVAL)
    
    def _collect_stats(self):
        """Collect statistics from TruBackend API"""
        try:
            response = requests.get(f"{TRUBACKEND_API_URL}/stats", timeout=5)
            if response.status_code == 200:
                stats = response.json()
                timestamp = datetime.now()
                
                # Store in history
                self.stats_history.append({
                    'timestamp': timestamp,
                    'stats': stats
                })
                
                # Update performance data
                self.performance_data['timestamps'].append(timestamp)
                self.performance_data['requests_processed'].append(
                    stats.get('processor_stats', {}).get('requests_processed', 0)
                )                self.performance_data['avg_processing_time'].append(
                    stats.get('processor_stats', {}).get('avg_processing_time', 0)
                )
                self.performance_data['total_tokens'].append(
                    stats.get('processor_stats', {}).get('total_tokens', 0)
                )
                
                # Simulate system metrics (in production, get from actual system)
                import psutil
                self.performance_data['memory_usage'].append(psutil.virtual_memory().percent)
                self.performance_data['cpu_usage'].append(psutil.cpu_percent())
                
                # Keep only last 100 data points
                for key in self.performance_data:
                    if len(self.performance_data[key]) > 100:
                        self.performance_data[key] = self.performance_data[key][-100:]
                
        except Exception as e:
            logger.error(f"Failed to collect stats: {e}")
    
    def get_system_status(self) -> Tuple[str, str, str]:
        """Get current system status"""
        try:
            response = requests.get(f"{TRUBACKEND_API_URL}/health", timeout=5)
            if response.status_code == 200:
                status = "🟢 Online"
                health_data = response.json()
                uptime = health_data.get('uptime', 'Unknown')
                last_check = datetime.now().strftime("%H:%M:%S")
            else:
                status = "🟡 Degraded"
                uptime = "Unknown"
                last_check = datetime.now().strftime("%H:%M:%S")
        except:
            status = "🔴 Offline"
            uptime = "Unknown"
            last_check = datetime.now().strftime("%H:%M:%S")
        
        return status, uptime, last_check
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current statistics"""
        try:
            response = requests.get(f"{TRUBACKEND_API_URL}/stats", timeout=5)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        
        return {
            'processor_stats': {
                'requests_processed': 0,
                'total_tokens': 0,
                'avg_processing_time': 0.0,
                'last_request': None
            },
            'system_info': {
                'model': 'gemma-3-4B-it-qat-GGUF',
                'max_context': 128000,
                'device': 'auto'
            }
        }    
    def analyze_email_api(self, subject: str, sender: str, recipient: str, content: str) -> Dict[str, Any]:
        """Analyze email using TruBackend API"""
        try:
            email_data = {
                'subject': subject,
                'sender': sender,
                'recipient': recipient,
                'content': content
            }
            
            response = requests.post(
                f"{TRUBACKEND_API_URL}/analyze-email",
                json=email_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # Store in history
                self.email_history.append({
                    'timestamp': datetime.now(),
                    'request': email_data,
                    'response': result
                })
                
                return result
            else:
                return {'error': f'API Error: {response.status_code}'}
                
        except Exception as e:
            return {'error': f'Request failed: {str(e)}'}
    
    def create_performance_chart(self) -> go.Figure:
        """Create performance monitoring chart"""
        if not self.performance_data['timestamps']:
            # Create empty chart
            fig = go.Figure()
            fig.add_annotation(
                text="No data available - Start monitoring to see performance metrics",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16)
            )
            fig.update_layout(
                title="Performance Metrics",
                xaxis_title="Time",
                yaxis_title="Value"
            )
            return fig
        
        fig = go.Figure()
        
        # Add traces for different metrics
        fig.add_trace(go.Scatter(
            x=self.performance_data['timestamps'],
            y=self.performance_data['requests_processed'],
            mode='lines+markers',
            name='Requests Processed',
            line=dict(color='#3498db')
        ))        
        fig.add_trace(go.Scatter(
            x=self.performance_data['timestamps'],
            y=[t * 1000 for t in self.performance_data['avg_processing_time']],  # Convert to ms
            mode='lines+markers',
            name='Avg Processing Time (ms)',
            line=dict(color='#e74c3c'),
            yaxis='y2'
        ))
        
        fig.add_trace(go.Scatter(
            x=self.performance_data['timestamps'],
            y=self.performance_data['memory_usage'],
            mode='lines+markers',
            name='Memory Usage (%)',
            line=dict(color='#f39c12'),
            yaxis='y3'
        ))
        
        fig.update_layout(
            title="Real-time Performance Metrics",
            xaxis_title="Time",
            yaxis=dict(title="Requests", side="left"),
            yaxis2=dict(title="Processing Time (ms)", side="right", overlaying="y"),
            yaxis3=dict(title="Memory (%)", side="right", overlaying="y", position=0.95),
            legend=dict(x=0.01, y=0.99),
            height=400
        )
        
        return fig
    
    def create_token_usage_chart(self) -> go.Figure:
        """Create token usage chart"""
        if not self.performance_data['timestamps']:
            fig = go.Figure()
            fig.add_annotation(
                text="No token data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False
            )
            return fig
        
        fig = px.line(
            x=self.performance_data['timestamps'],
            y=self.performance_data['total_tokens'],
            title="Token Usage Over Time",
            labels={'x': 'Time', 'y': 'Total Tokens'}
        )
        
        fig.update_layout(height=300)
        return fig
    
    def create_email_analysis_history(self) -> pd.DataFrame:
        """Create email analysis history DataFrame"""
        if not self.email_history:
            return pd.DataFrame(columns=['Timestamp', 'Subject', 'Sentiment', 'Priority', 'Processing Time'])
        
        data = []
        for entry in self.email_history[-20:]:  # Last 20 entries            data.append({
                'Timestamp': entry['timestamp'].strftime("%H:%M:%S"),
                'Subject': entry['request']['subject'][:50] + '...' if len(entry['request']['subject']) > 50 else entry['request']['subject'],
                'Sentiment': entry['response'].get('sentiment', 'N/A'),
                'Priority': entry['response'].get('priority', 'N/A'),
                'Processing Time': f"{entry['response'].get('processing_time', 0):.3f}s"
            })
        
        return pd.DataFrame(data)

# Initialize the interface
interface = TruBackendGradioInterface()

def create_main_interface():
    """Create the main Gradio interface"""
    
    # Custom CSS for better styling
    custom_css = """
    .gradio-container {
        max-width: 1400px !important;
    }
    .status-online { color: #27ae60; font-weight: bold; }
    .status-offline { color: #e74c3c; font-weight: bold; }
    .status-degraded { color: #f39c12; font-weight: bold; }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
    }
    .metric-value {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .metric-label {
        font-size: 0.9em;
        opacity: 0.9;
    }
    """
    
    with gr.Blocks(
        title="🚀 TruBackend Comprehensive Management Interface",
        theme=gr.themes.Soft(),
        css=custom_css
    ) as demo:
        
        # Header
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 2.5em;">🚀 TruBackend Management Interface</h1>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Comprehensive Email Intelligence & AI Model Management</p>
        </div>
        """)        
        # Main tabs
        with gr.Tabs() as main_tabs:
            
            # 📊 Dashboard Tab
            with gr.Tab("📊 Dashboard", id="dashboard"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### 🔍 System Status")
                        system_status = gr.Textbox(label="Status", interactive=False)
                        system_uptime = gr.Textbox(label="Uptime", interactive=False)
                        last_check = gr.Textbox(label="Last Check", interactive=False)
                        
                        # Control buttons
                        with gr.Row():
                            start_monitoring_btn = gr.Button("▶️ Start Monitoring", variant="primary")
                            stop_monitoring_btn = gr.Button("⏹️ Stop Monitoring", variant="secondary")
                            refresh_btn = gr.Button("🔄 Refresh", variant="secondary")
                    
                    with gr.Column(scale=2):
                        gr.Markdown("### 📈 Real-time Metrics")
                        with gr.Row():
                            requests_metric = gr.Number(label="📧 Requests Processed", interactive=False)
                            tokens_metric = gr.Number(label="🔤 Total Tokens", interactive=False)
                        with gr.Row():
                            avg_time_metric = gr.Number(label="⏱️ Avg Processing Time (s)", interactive=False, precision=3)
                            confidence_metric = gr.Number(label="🎯 Avg Confidence", interactive=False, precision=2)
                
                # Performance charts
                gr.Markdown("### 📊 Performance Analytics")
                performance_chart = gr.Plot(label="Performance Metrics")
                
                with gr.Row():
                    token_chart = gr.Plot(label="Token Usage", scale=1)
                    system_info = gr.JSON(label="System Information", scale=1)
            
            # 📧 Email Analysis Tab
            with gr.Tab("📧 Email Analysis", id="email"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### ✉️ Email Input")
                        email_subject = gr.Textbox(
                            label="Subject",
                            placeholder="Enter email subject...",
                            lines=1
                        )
                        email_sender = gr.Textbox(
                            label="Sender",
                            placeholder="<EMAIL>",
                            lines=1
                        )
                        email_recipient = gr.Textbox(
                            label="Recipient",
                            placeholder="<EMAIL>",
                            lines=1
                        )
                        email_content = gr.Textbox(
                            label="Content",
                            placeholder="Enter email content for analysis...",
                            lines=8
                        )                        
                        analyze_btn = gr.Button("🔍 Analyze Email", variant="primary", size="lg")
                        clear_email_btn = gr.Button("🗑️ Clear", variant="secondary")
                    
                    with gr.Column(scale=1):
                        gr.Markdown("### 📋 Analysis Results")
                        analysis_status = gr.Textbox(label="Status", interactive=False)
                        sentiment_result = gr.Textbox(label="😊 Sentiment", interactive=False)
                        priority_result = gr.Textbox(label="⚡ Priority", interactive=False)
                        confidence_result = gr.Number(label="🎯 Confidence", interactive=False, precision=2)
                        processing_time_result = gr.Number(label="⏱️ Processing Time (s)", interactive=False, precision=3)
                        
                        gr.Markdown("### 📝 Summary & Entities")
                        summary_result = gr.Textbox(label="Summary", lines=3, interactive=False)
                        entities_result = gr.Textbox(label="Entities", lines=2, interactive=False)
                
                # Email history
                gr.Markdown("### 📚 Analysis History")
                email_history_df = gr.Dataframe(
                    headers=['Timestamp', 'Subject', 'Sentiment', 'Priority', 'Processing Time'],
                    label="Recent Analyses",
                    interactive=False
                )
                
                # Example emails
                gr.Markdown("### 💡 Example Emails")
                with gr.Row():
                    example1_btn = gr.Button("📧 Customer Complaint")
                    example2_btn = gr.Button("📧 Meeting Request")
                    example3_btn = gr.Button("📧 Sales Inquiry")

        # Event handlers
        def update_dashboard():
            """Update dashboard metrics"""
            status, uptime, check_time = interface.get_system_status()
            stats = interface.get_current_stats()
            
            processor_stats = stats.get('processor_stats', {})
            
            return (
                status,
                uptime,
                check_time,
                processor_stats.get('requests_processed', 0),
                processor_stats.get('total_tokens', 0),
                processor_stats.get('avg_processing_time', 0.0),
                0.85,  # Mock confidence
                interface.create_performance_chart(),
                interface.create_token_usage_chart(),
                stats.get('system_info', {})
            )
        
        def start_monitoring():
            """Start monitoring"""
            interface.start_monitoring()
            return "🟢 Monitoring started"
        
        def stop_monitoring():
            """Stop monitoring"""
            interface.stop_monitoring()
            return "🔴 Monitoring stopped"        
        def analyze_email(subject, sender, recipient, content):
            """Analyze email"""
            if not subject or not content:
                return (
                    "❌ Error: Subject and content are required",
                    "", "", 0, 0, "", "",
                    interface.create_email_analysis_history()
                )
            
            result = interface.analyze_email_api(subject, sender, recipient, content)
            
            if 'error' in result:
                return (
                    f"❌ {result['error']}",
                    "", "", 0, 0, "", "",
                    interface.create_email_analysis_history()
                )
            
            return (
                "✅ Analysis completed",
                result.get('sentiment', ''),
                result.get('priority', ''),
                result.get('confidence', 0),
                result.get('processing_time', 0),
                result.get('summary', ''),
                ', '.join(result.get('entities', [])),
                interface.create_email_analysis_history()
            )
        
        def clear_email_form():
            """Clear email form"""
            return "", "", "", ""
        
        def load_example_email(example_type):
            """Load example email"""
            examples = {
                "complaint": {
                    "subject": "Urgent: HVAC System Failure - Immediate Assistance Required",
                    "sender": "<EMAIL>",
                    "recipient": "<EMAIL>",
                    "content": "I am extremely frustrated with your service! Our HVAC system has been down for 3 days and no one has responded to our calls. This is unacceptable and we demand immediate action. Our office temperature is unbearable and affecting our business operations."
                },
                "meeting": {
                    "subject": "Weekly HVAC Maintenance Review Meeting",
                    "sender": "<EMAIL>",
                    "recipient": "<EMAIL>",
                    "content": "Hi team, let's schedule our weekly maintenance review meeting for Thursday at 2 PM. We'll discuss the recent service calls, upcoming installations, and review our performance metrics. Please bring your weekly reports."
                },
                "inquiry": {
                    "subject": "Quote Request for Commercial HVAC Installation",
                    "sender": "<EMAIL>",
                    "recipient": "<EMAIL>",
                    "content": "Good morning, we are planning a new office building and need a quote for a complete HVAC system installation. The building is 50,000 sq ft with 3 floors. Could you please provide a detailed quote including equipment, installation, and maintenance options?"
                }
            }
            
            example = examples.get(example_type, examples["complaint"])
            return example["subject"], example["sender"], example["recipient"], example["content"]        
        # Connect event handlers
        refresh_btn.click(
            update_dashboard,
            outputs=[
                system_status, system_uptime, last_check,
                requests_metric, tokens_metric, avg_time_metric, confidence_metric,
                performance_chart, token_chart, system_info
            ]
        )
        
        start_monitoring_btn.click(
            start_monitoring,
            outputs=[system_status]
        )
        
        stop_monitoring_btn.click(
            stop_monitoring,
            outputs=[system_status]
        )
        
        analyze_btn.click(
            analyze_email,
            inputs=[email_subject, email_sender, email_recipient, email_content],
            outputs=[
                analysis_status, sentiment_result, priority_result,
                confidence_result, processing_time_result,
                summary_result, entities_result, email_history_df
            ]
        )
        
        clear_email_btn.click(
            clear_email_form,
            outputs=[email_subject, email_sender, email_recipient, email_content]
        )
        
        example1_btn.click(
            lambda: load_example_email("complaint"),
            outputs=[email_subject, email_sender, email_recipient, email_content]
        )
        
        example2_btn.click(
            lambda: load_example_email("meeting"),
            outputs=[email_subject, email_sender, email_recipient, email_content]
        )
        
        example3_btn.click(
            lambda: load_example_email("inquiry"),
            outputs=[email_subject, email_sender, email_recipient, email_content]
        )
        
        # Auto-refresh dashboard every 10 seconds
        demo.load(
            update_dashboard,
            outputs=[
                system_status, system_uptime, last_check,
                requests_metric, tokens_metric, avg_time_metric, confidence_metric,
                performance_chart, token_chart, system_info
            ],
            every=10
        )    
    return demo

if __name__ == "__main__":
    print("🚀 Starting TruBackend Comprehensive Gradio Interface...")
    print(f"📊 Interface URL: http://localhost:{GRADIO_PORT}")
    print(f"🔗 TruBackend API: {TRUBACKEND_API_URL}")
    
    # Create and launch the interface
    demo = create_main_interface()
    
    try:
        demo.launch(
            server_name="0.0.0.0",
            server_port=GRADIO_PORT,
            share=False,
            show_error=True,
            quiet=False,
            favicon_path=None,
            ssl_verify=False
        )
    except KeyboardInterrupt:
        print("\n🛑 Shutting down Gradio interface...")
        interface.stop_monitoring()
    except Exception as e:
        print(f"❌ Failed to start interface: {e}")
        interface.stop_monitoring()